import json
from fastapi import APIRouter, HTTPException, Path, Query, Depends
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime, date
import os
from database.db_operations import execute_query
from api.visualize.pickle_visualize import load_pickle
from api.auth.auth import get_current_user
from api.dataset.evaluation_sets import extract_bag_name_from_pkl_name
import cv2
import numpy as np
import base64
from api.visualize.calib_reader import CalibReaderDict

calib_reader_dict = CalibReaderDict()
# calib_reader_dict={}
# calib_reader = CalibReader()
# calib=calib_reader.get_calib(scale=[0.2666,0.2666], offset_x=0, offset_y=160*2160/576)
# ego2img_array=calib['front_wide']['ego2img']
router = APIRouter(prefix="/api/annotation", tags=["annotation"])


class EvaluationSetProgress(BaseModel):
    evaluation_set_id: int
    evaluation_set_name: str
    total_pkls: int
    annotated_pkls: int
    progress_percentage: float
    last_updated: Optional[datetime] = None


class EmployeeProgress(BaseModel):
    employee_id: str
    annotation_date: date
    annotated_pkls_count: int


class AnnotationProgressResponse(BaseModel):
    success: bool
    evaluation_sets_progress: List[EvaluationSetProgress]
    employee_daily_progress: List[EmployeeProgress]


class PdpPathAnnotation(BaseModel):
    pkl_id: int
    path_index: int
    annotation: str  # "good" or "bad"
    inference_config_id: Optional[int] = 3  # 推理配置ID
    delete_annotation: Optional[bool] = False  # 是否删除标注
    evaluation_set_id: Optional[int] = None  # 评测集ID,用于关联评测集

class PDPSelectedObjects(BaseModel):
    pkl_id: int
    selected_object_ids: List[str]
    delete_annotation: Optional[bool] = False
    evaluation_set_id: Optional[int] = None
class PicklePdpPathsRequest(BaseModel):
    pickle_path: str


class MarkAsDirtyRequest(BaseModel):
    pkl_id: int
    is_dirty: bool = True


@router.post("/mark-dirty")
async def mark_as_dirty_data(request: MarkAsDirtyRequest):
    """
    标记或取消标记PKL文件为脏数据
    """
    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 更新脏数据标记
    update_query = """
    UPDATE evaluation_case_pool
    SET dirty_data = %s
    WHERE id = %s
    """
    params = (request.is_dirty, request.pkl_id)
    result = execute_query(update_query, params)

    if not result["success"]:
        raise HTTPException(
            status_code=500, detail=f"更新脏数据状态时出错: {result['error']}"
        )

    return {"success": True, "message": "数据状态已更新", "is_dirty": request.is_dirty}


@router.get("/pdp-paths/{pkl_id}")
async def get_pdp_paths(
    pkl_id: int,
    evaluation_set_id: Optional[int] = Query(
        None, description="评测集ID，用于过滤标注结果"
    ),
):
    """获取指定PKL文件中的所有PDP路径信息,并包含可视化所需的路径点数据和e2e_image图像"""
    # 查询pkl文件路径
    query = """
    SELECT pkl_dir, pkl_name, dirty_data, vin FROM evaluation_case_pool WHERE id = %s
    """
    result = execute_query(query, (pkl_id,), fetch_one=True)

    if not result["success"] or not result["data"]:
        raise HTTPException(status_code=404, detail="找不到指定的PKL文件")

    pkl_dir, pkl_name, is_dirty, vin = result["data"]
    pickle_path = os.path.join(pkl_dir, pkl_name)

    # 检查文件是否存在
    if not os.path.exists(pickle_path):
        raise HTTPException(status_code=404, detail="PKL文件不存在")

    # try:
    if True:
        # 加载pickle文件
        data = load_pickle(pickle_path)
        ego_v = data["ego_input"][0] ** 2 + data["ego_input"][1] ** 2
        ego_v = ego_v**0.5
        ego_s = ego_v * 6
        ego_default_s = 40
        ego_final_s = max(ego_s, ego_default_s)
        ego_final_index = int(ego_final_s / 2)
        pdp_paths = data.get("pdp_path", [])

        # np.ndarray(200,2)
        ego_gt = data.get("path_point", [])
        ego_gt_mask = data.get("path_mask", [])

        # 处理e2e_image图像
        image_data = None
        image = data.get("e2e_image", None)
        ego_yaw = -1.0 * data.get("feature_obj", None)[0, 0, 10]
        if image:
            np_image = image.get("image", None)
            if np_image is not None and isinstance(np_image, np.ndarray):
                # 将numpy数组格式转换为适合前端显示的格式
                try:
                    # 转换为RGB图像
                    if len(np_image.shape) == 3 and np_image.shape[0] == 3:
                        # 如果是(3, height, width)格式，转换为(height, width, 3)
                        np_image = np.transpose(np_image, (1, 2, 0))

                    # 确保图像数据在0-255范围内
                    if np_image.max() <= 1.0:
                        np_image = (np_image * 255).astype(np.uint8)
                    np_image = cv2.cvtColor(np_image, cv2.COLOR_YUV2BGR)
                    # print('...imgs',np_image.shape)
                    # np_image = cv2.cvtColor(np_image, cv2.COLOR_BGR2RGB)
                    # 编码为JPEG图像
                    _, buffer = cv2.imencode(".jpg", np_image)
                    image_data = base64.b64encode(buffer).decode("utf-8")
                except Exception as e:
                    print(f"处理图像出错: {str(e)}")

        # # 查询现有标注
        # if evaluation_set_id is not None:
        #     query = """
        #     SELECT path_index, annotation
        #     FROM pdp_path_annotation
        #     WHERE pkl_id = %s AND evaluation_set_id = %s
        #     """
        #     annotations_result = execute_query(
        #         query, (pkl_id, evaluation_set_id), fetch_all=True
        #     )
        # else:
        # 如果没有提供evaluation_set_id，使用原来的查询方式（为了向后兼容）
        query = """
        SELECT path_index, annotation
        FROM pdp_path_annotation 
        WHERE pkl_id = %s
        """
        annotations_result = execute_query(query, (pkl_id,), fetch_all=True)

        # 转换为字典以便快速查找
        annotations = {}
        if annotations_result["success"] and annotations_result["data"]:
            for path_index, annotation in annotations_result["data"]:
                annotations[path_index] = {"annotation": annotation}

        # 查询最新的标注更新信息
        latest_annotation_info = None
        # if evaluation_set_id is not None:
        #     latest_query = """
        #     SELECT employee_id, updated_at
        #     FROM pdp_path_annotation
        #     WHERE pkl_id = %s AND evaluation_set_id = %s
        #     ORDER BY updated_at DESC
        #     LIMIT 1
        #     """
        #     latest_result = execute_query(
        #         latest_query, (pkl_id, evaluation_set_id), fetch_one=True
        #     )
        #     if latest_result["success"] and latest_result["data"]:
        #         latest_annotation_info = {
        #             "employee_id": latest_result["data"][0],
        #             "updated_at": latest_result["data"][1]
        #         }
        # else:
        latest_query = """
        SELECT employee_id, updated_at
        FROM pdp_path_annotation 
        WHERE pkl_id = %s
        ORDER BY updated_at DESC
        LIMIT 1
        """
        latest_result = execute_query(latest_query, (pkl_id,), fetch_one=True)
        # print(latest_result)
        if latest_result["success"] and latest_result["data"]:
            latest_annotation_info = {
                "employee_id": latest_result["data"][0],
                "updated_at": latest_result["data"][1],
            }

        # 准备返回数据,同时包含路径点坐标
        paths_info = []

        if ego_gt is not None and ego_gt_mask is not None and ego_gt_mask[-1] >= 0.5:
            if len(ego_gt) > 50:
                step = max(1, len(ego_gt) // 50)
                # ego_gt0 = ego_gt[:4]
                ego_gt = ego_gt[::step]
                # ego_gt = ego_gt0 + ego_gt1
            gt_visualization_points = []
            gt_middle_point = None
            for i, (point, mask) in enumerate(zip(ego_gt, ego_gt_mask)):
                if mask >= 0.5:  # 有效点
                    if i == ego_final_index:
                        gt_middle_point = [float(point[0]), float(point[1]), 0.0]
                    gt_visualization_points.append(
                        [float(point[0]), float(point[1]), 0.0]
                    )
            # gt_visualization_points 下采样到50个点 当前200个

            # print('..gt path',gt_visualization_points[-1][0],gt_visualization_points[-1][1])
            if len(gt_visualization_points) > 0:
                gt_path_info = {
                    "index": -1,  # GT路径使用-1作为索引
                    "probability": 1.0,  # GT路径概率设为1.0
                    "points_count": len(gt_visualization_points),
                    "annotation": annotations.get(-1, None),  # 查找GT路径的标注
                    "visualization_points": gt_visualization_points,
                    "middle_point": gt_middle_point,
                    "is_ground_truth": True,  # 标识为GT路径
                }
                paths_info.append(gt_path_info)
        if pdp_paths and len(pdp_paths)>0:    
            for i, path in enumerate(pdp_paths):
                middle_point = None
                prob = float(path.get("prob", 0))
                # if prob == 0.0:
                #     continue
                raw_points = path.get("raw_points", [])
                points_count = len(raw_points)

                # 提取可视化所需的点数据
                visualization_points = []
                index = 0
                for point in raw_points:
                    if index == ego_final_index:
                        middle_point = point
                    # 确保每个点至少有x,y坐标
                    if len(point) >= 2:
                        # 将点转换为[x,y,z]格式,如果没有z坐标则默认为0
                        visualization_point = [
                            float(point[0]),
                            float(point[1]),
                            float(point[2]) if len(point) > 2 else 0.0,
                        ]
                        visualization_points.append(visualization_point)
                    index += 1

                path_info = {
                    "index": i,
                    "probability": prob,
                    "points_count": points_count,
                    "annotation": annotations.get(i, None),
                    "visualization_points": visualization_points,  # 添加可视化点数据
                    "middle_point": middle_point,
                    "is_ground_truth": False,  # 标识为非GT路径
                }
                paths_info.append(path_info)
            # 查询该 PKL 文件的选中对象
        selected_objects_query = """
        SELECT selected_object_ids FROM pdp_selected_objects WHERE pkl_id = %s
        """
        selected_objects_result = execute_query(
            selected_objects_query, (pkl_id,), fetch_one=True
        )
        selected_object_ids = []
        if selected_objects_result["success"] and selected_objects_result["data"]:
            try:
                selected_object_ids = (
                    json.loads(selected_objects_result["data"][0])
                    if selected_objects_result["data"][0]
                    else []
                )
                # print(selected_object_ids)
            except (json.JSONDecodeError, TypeError):
                selected_object_ids = []
        # ego=ego2img_array.tolist() if ego2img_array is not None else None
        # print('ego2img_array', ego)
        ego2img_array = calib_reader_dict.get_calib(
            vin=vin, scale=[0.2666, 0.2666], offset_x=0, offset_y=160 * 2160 / 576
        )["front_wide"]["ego2img"]
        # print('...ego2img',ego2img_array)
        return {
            "success": True,
            "paths": paths_info,
            "total": len(paths_info),
            "is_dirty": is_dirty,
            "image_data": image_data,  # 添加图像数据
            "ego_yaw": ego_yaw,
            "ego2img": ego2img_array.tolist() if ego2img_array is not None else None,
            "latest_annotation_info": latest_annotation_info,  # 添加最新的标注信息
            "selected_object_ids": selected_object_ids,
        }

    # except Exception as e:
    #     raise HTTPException(status_code=500, detail=f"处理PKL文件时出错: {str(e)}")


@router.post("/pdp-paths")
async def annotate_pdp_path(
    annotation: PdpPathAnnotation,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """
    标注PDP路径（新增或更新）或删除标注
    """
    # 验证标注值
    if annotation.annotation not in ["good", "bad", "unknown"]:
        raise HTTPException(status_code=400, detail="标注值必须是'good'或'bad'")

    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (annotation.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 如果是删除操作
    if annotation.delete_annotation:
        # delete_query = """
        # DELETE FROM pdp_path_annotation
        # WHERE pkl_id = %s AND path_index = %s AND inference_config_id = %s AND evaluation_set_id = %s
        # """
        delete_query = """
        DELETE FROM pdp_path_annotation 
        WHERE pkl_id = %s AND path_index = %s AND inference_config_id = %s 
        """
        params = (
            annotation.pkl_id,
            annotation.path_index,
            annotation.inference_config_id,
            # annotation.evaluation_set_id,
        )
        result = execute_query(delete_query, params)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"删除标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注已删除"}
    else:
        # 插入或更新标注
        if current_user:
            query = """
            INSERT INTO pdp_path_annotation 
            (pkl_id, path_index, annotation, inference_config_id, evaluation_set_id, 
                 employee_id, created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE 
            annotation = VALUES(annotation),
            employee_id = VALUES(employee_id),
            evaluation_set_id=VALUES(evaluation_set_id),
            updated_at = CURRENT_TIMESTAMP
            """
            params = (
                annotation.pkl_id,
                annotation.path_index,
                annotation.annotation,
                annotation.inference_config_id,
                annotation.evaluation_set_id
                if annotation.evaluation_set_id is not None
                else None,
                current_user.get("employee_id", ""),
            )
        else:
            query = """
            INSERT INTO pdp_path_annotation 
            (pkl_id, path_index, annotation, inference_config_id, evaluation_set_id, 
             created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE 
            annotation = VALUES(annotation),
            updated_at = CURRENT_TIMESTAMP
            evaluation_set_id=VALUES(evaluation_set_id)
            """
            params = (
                annotation.pkl_id,
                annotation.path_index,
                annotation.annotation,
                annotation.inference_config_id,
                annotation.evaluation_set_id
                if annotation.evaluation_set_id is not None
                else None,
            )

        result = execute_query(query, params)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"保存标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注保存成功"}


@router.post("/pdp-selected-objects")
async def save_pdp_selected_objects(
    request: PDPSelectedObjects,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """保存 PKL 文件的选中对象列表"""
    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)
    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")
    # 如果选中的对象为空，则删除记录
    if not request.selected_object_ids:
        delete_query = "DELETE FROM pdp_selected_objects WHERE pkl_id = %s"
        result = execute_query(delete_query, (request.pkl_id,))
        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"删除空选中对象记录时出错: {result['error']}"
            )
        return {"success": True, "message": "空选中对象记录已删除"}
    if current_user:
        query = """
        INSERT INTO pdp_selected_objects
        (pkl_id, selected_object_ids, evaluation_set_id, employee_id, updated_at)
        VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        selected_object_ids = VALUES(selected_object_ids),
        updated_at = CURRENT_TIMESTAMP
        """

        params = (
            request.pkl_id,
            json.dumps(request.selected_object_ids),
            request.evaluation_set_id
            if request.evaluation_set_id is not None
            else None,
            current_user.get("employee_id", ""),
        )
    else:
        query = """
        INSERT INTO pdp_selected_objects
        (pkl_id, selected_object_ids, evaluation_set_id, employee_id, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        selected_object_ids = VALUES(selected_object_ids),
        updated_at = CURRENT_TIMESTAMP
        """

        params = (
            request.pkl_id,
            json.dumps(request.selected_object_ids),
            request.evaluation_set_id
            if request.evaluation_set_id is not None
            else None,
        )
    result = execute_query(query, params)

    if not result["success"]:
        raise HTTPException(
            status_code=500, detail=f"保存选中对象时出错: {result['error']}"
        )

    return {"success": True, "message": "选中对象保存成功"}
@router.get("/export-annotations")
async def export_annotations(
    annotation_type: Optional[str] = Query(
        None, description="标注类型过滤，可选值：good、bad、unknown"
    ),
    inference_config_id: Optional[int] = Query(None, description="推理配置ID过滤"),
):
    """
    导出所有标注结果，以pkl_dir+pkl_name为索引

    参数:
    - annotation_type: 可选，按标注类型过滤 (good, bad, unknown)
    - inference_config_id: 可选，按推理配置ID过滤

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据
    """
    try:
        # 构建查询条件
        query_conditions = []
        params = []

        if annotation_type:
            if annotation_type not in ["good", "bad", "unknown"]:
                raise HTTPException(
                    status_code=400, detail="标注类型必须是 'good', 'bad' 或 'unknown'"
                )
            query_conditions.append("a.annotation = %s")
            params.append(annotation_type)

        if inference_config_id is not None:
            query_conditions.append("a.inference_config_id = %s")
            params.append(inference_config_id)

        # 构建WHERE子句
        where_clause = ""
        if query_conditions:
            where_clause = "WHERE " + " AND ".join(query_conditions)

        # 查询标注和对应的pickle文件路径
        query = f"""
        SELECT 
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            a.path_index,
            a.annotation,
            a.inference_config_id,
            a.updated_at,
            a.employee_id
        FROM 
            pdp_path_annotation a
        JOIN 
            evaluation_case_pool p ON a.pkl_id = p.id
        {where_clause}
        ORDER BY 
            pickle_path, a.path_index
        """

        result = execute_query(query, params, fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据，按pickle_path分组
        annotations_by_path = {}

        for row in result["data"]:
            (
                pickle_path,
                path_index,
                annotation,
                inference_config_id,
                updated_at,
                employee_id,
            ) = row

            # 确保路径存在于字典中
            if pickle_path not in annotations_by_path:
                annotations_by_path[pickle_path] = []
            # 添加标注信息
            annotations_by_path[pickle_path].append(
                {
                    "path_index": path_index,
                    "annotation": annotation,
                    "inference_config_id": inference_config_id,
                }
            )
        return {
            "success": True,
            "total": len(result["data"]),
            "annotation_count": len(annotations_by_path),
            "annotations": annotations_by_path,
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")


@router.get("/export-annotations/{evaluation_set_id}")
async def export_annotations_by_set(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """
    导出指定评测集下的标注结果，以pkl_dir+pkl_name为索引，同时包含检查状态

    参数:
    - evaluation_set_id: 路径参数，评测集ID

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据，每条路径包含is_checked字段
    """
    try:
        # 检查评测集ID是否存在
        check_set_query = "SELECT id FROM evaluation_set WHERE id = %s"
        set_exists_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )
        if not set_exists_result["success"] or not set_exists_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        # 修改查询，添加检查状态信息
        # query = """
        # SELECT
        #     CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
        #     a.path_index,
        #     a.annotation,
        #     a.inference_config_id,
        #     a.updated_at,
        #     a.employee_id,
        #     COALESCE(escp.is_checked, FALSE) as is_checked,
        #     escp.checked_at,
        #     escp.checked_by
        # FROM
        #     pdp_path_annotation a
        # JOIN
        #     evaluation_case_pool p ON a.pkl_id = p.id
        # JOIN
        #     evaluation_set_case_pool escp ON a.pkl_id = escp.evaluation_case_id
        #     AND escp.evaluation_set_id = %s
        # WHERE
        #     a.evaluation_set_id = %s
        # ORDER BY
        #     pickle_path, a.path_index
        # """
        query = """
        SELECT 
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            a.path_index,
            a.annotation,
            a.inference_config_id,
            a.updated_at,
            a.employee_id,
            COALESCE(escp.is_checked, FALSE) as is_checked,
            escp.checked_at,
            escp.checked_by,
            pso.selected_object_ids
        FROM 
            pdp_path_annotation a
        JOIN 
            evaluation_case_pool p ON a.pkl_id = p.id
        JOIN 
            evaluation_set_case_pool escp ON a.pkl_id = escp.evaluation_case_id 
            AND escp.evaluation_set_id = %s
        LEFT JOIN
            pdp_selected_objects pso ON a.pkl_id = pso.pkl_id
        ORDER BY 
            pickle_path, a.path_index
        """

        result = execute_query(query, (evaluation_set_id,), fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据，按pickle_path分组
        annotations_by_path = {}
        if result["data"]:
            for row in result["data"]:
                (
                    pickle_path,
                    path_index,
                    annotation,
                    db_inference_config_id,
                    updated_at,
                    employee_id,
                    is_checked,
                    checked_at,
                    checked_by,
                    select_objected_ids,
                ) = row
                id_list = []
                # transfer objected_ids to list
                if select_objected_ids is not None:
                    select_objected_ids = json.loads(select_objected_ids)
                    id_list = [int(id.split("_")[1]) for id in select_objected_ids]
                # 确保路径存在于字典中
                if pickle_path not in annotations_by_path:
                    annotations_by_path[pickle_path] = {
                        "metadata":{
                            "last_updated": None,
                            "last_updated_by": None,
                            "is_checked": bool(is_checked),  # 确保是布尔值
                            "checked_at": checked_at.isoformat() if checked_at else None,
                            "checked_by": checked_by,
                            
                        },
                        "selected_object_ids": id_list,
                        "annotations": [],
                        }
                    

                # 添加标注信息，包含检查状态
                if path_index is not None:
                    annotations_by_path[pickle_path]["annotations"].append(
                        {
                            "path_index": path_index,
                            "annotation": annotation,
                            "inference_config_id": db_inference_config_id,
                            "is_checked": bool(is_checked),  # 确保是布尔值
                            "checked_at": checked_at.isoformat() if checked_at else None,
                            "checked_by": checked_by,
                        }
                    )
                    # 更新最近更新时间和人员
                    if updated_at and (
                        not annotations_by_path[pickle_path]["metadata"]["last_updated"]
                        or updated_at.isoformat()
                        > annotations_by_path[pickle_path]["metadata"]["last_updated"]
                    ):
                        annotations_by_path[pickle_path]["metadata"]["last_updated"] = (
                            updated_at.isoformat()
                        )
                        annotations_by_path[pickle_path]["metadata"]["last_updated_by"] = employee_id
            return {
                "success": True,
                "total": len(result["data"]),
                "annotation_count": len(annotations_by_path),
                "annotations": annotations_by_path,
            }

        else:
            pass
            # query = """
            # SELECT 
            #     CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            #     pso.selected_object_ids
            # FROM 
            #     evaluation_case_pool p
            # JOIN 
            #     evaluation_set_case_pool escp  ON p.id = escp.evaluation_case_id 
            #     AND escp.evaluation_set_id = %s
            # LEFT JOIN
            #     pdp_selected_objects pso ON p.id = pso.pkl_id
            # WHERE
            #     escp.is_checked = TRUE
            # ORDER BY 
            #     pickle_path
            # """
            # result = execute_query(query, (evaluation_set_id,), fetch_all=True)

            # if not result["success"]:
            #     raise HTTPException(
            #         status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            #     )
            # pkl_list = []
            # for row in result["data"]:
            #     id_list = []
            #     # transfer objected_ids to list
            #     if select_objected_ids is not None:
            #         select_objected_ids = json.loads(select_objected_ids)
            #         id_list = [int(id.split("_")[1]) for id in select_objected_ids]
            #     pkl_list.append({
            #         "pkl_path": row[0],
            #         "selected_object_ids": id_list,
            #     })
            # return {
            #     "success": True,
            #     "total": len(pkl_list),
            #     "pkl_list": pkl_list,
            # }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")
@router.get("/export-bag-list/{evaluation_set_id}")
async def export_bag_list(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """
    导出指定评测集下的标注结果，以pkl_dir+pkl_name为索引，同时包含检查状态

    参数:
    - evaluation_set_id: 路径参数，评测集ID

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据，每条路径包含is_checked字段
    """
    try:
        # 检查评测集ID是否存在
        check_set_query = "SELECT id FROM evaluation_set WHERE id = %s"
        set_exists_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )
        if not set_exists_result["success"] or not set_exists_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        query = """
            SELECT DISTINCT ecp.id, ecp.pkl_name
            FROM evaluation_case_pool ecp 
            JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id 
            WHERE escp.evaluation_set_id = %s
            ORDER BY ecp.id DESC
            """

        result = execute_query(query, (evaluation_set_id,), fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )
        bag_list=set()

        if result["data"]:
            for row in result["data"]:
                id=row[0]
                bag_name=extract_bag_name_from_pkl_name(row[1])
                bag_list.add(bag_name)
            return {
                "success": True,
                "total": len(bag_list),
                "bag_list": list(bag_list),
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")

@router.get("/export-annotations-checked/{evaluation_set_id}")
async def export_annotations_by_set_checked(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """
    导出指定评测集下的标注结果，以pkl_dir+pkl_name为索引，同时包含检查状态

    参数:
    - evaluation_set_id: 路径参数，评测集ID

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据，每条路径包含is_checked字段
    """
    try:
        # 检查评测集ID是否存在
        check_set_query = "SELECT id FROM evaluation_set WHERE id = %s"
        set_exists_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )
        if not set_exists_result["success"] or not set_exists_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        query = """
            SELECT 
                CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
                pso.selected_object_ids
            FROM 
                evaluation_case_pool p
            JOIN 
                evaluation_set_case_pool escp  ON p.id = escp.evaluation_case_id 
                AND escp.evaluation_set_id = %s
            LEFT JOIN
                pdp_selected_objects pso ON p.id = pso.pkl_id
            WHERE
                escp.is_checked = TRUE
            ORDER BY 
                pickle_path
            """
        result = execute_query(query, (evaluation_set_id,), fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )
        pkl_list = []
        for row in result["data"]:
            select_objected_ids=row[1]
            id_list = []
            # transfer objected_ids to list
            if select_objected_ids is not None:
                select_objected_ids = json.loads(select_objected_ids)
                id_list = [int(id.split("_")[1]) for id in select_objected_ids]
            pkl_list.append({
                "pkl_path": row[0],
                "selected_object_ids": id_list,
            })
        return {
            "success": True,
            "total": len(pkl_list),
            "pkl_list": pkl_list,
        }
    except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(status_code=500, detail=f"导出检查数据时出错: {str(e)}")   
@router.get("/progress", response_model=AnnotationProgressResponse)
async def get_annotation_progress(
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    employee_id: Optional[str] = Query(None, description="员工ID过滤"),
    evaluation_set_id: Optional[int] = Query(None, description="评测集ID过滤"),
    current_user: Optional[dict] = Depends(get_current_user),
):
    """
    获取标注进展数据
    """
    try:
        # 1. 获取评测集标注进度
        evaluation_sets_progress = []

        # 构建评测集查询条件
        eval_set_conditions = []
        eval_set_params = []

        if evaluation_set_id is not None:
            eval_set_conditions.append("es.id = %s")
            eval_set_params.append(evaluation_set_id)

        eval_set_where = ""
        if eval_set_conditions:
            eval_set_where = "WHERE " + " AND ".join(eval_set_conditions)

        # 查询评测集的标注进度
        eval_progress_query = f"""
        SELECT 
            es.id as evaluation_set_id,
            es.set_name as evaluation_set_name,
            COUNT(DISTINCT escp.evaluation_case_id) as total_pkls,
            COUNT(DISTINCT pa.pkl_id) as annotated_pkls,
            CASE 
                WHEN COUNT(DISTINCT escp.evaluation_case_id) > 0 
                THEN ROUND((COUNT(DISTINCT pa.pkl_id) / COUNT(DISTINCT escp.evaluation_case_id)) * 100, 2)
                ELSE 0 
            END as progress_percentage,
            MAX(pa.updated_at) as last_updated
        FROM 
            evaluation_set es
        LEFT JOIN 
            evaluation_set_case_pool escp ON es.id = escp.evaluation_set_id
        LEFT JOIN 
            pdp_path_annotation pa ON escp.evaluation_case_id = pa.pkl_id AND pa.evaluation_set_id = es.id
        {eval_set_where}
        GROUP BY 
            es.id, es.set_name
        ORDER BY 
            es.id
        """

        eval_result = execute_query(
            eval_progress_query, tuple(eval_set_params), fetch_all=True
        )

        if eval_result["success"] and eval_result["data"]:
            for row in eval_result["data"]:
                evaluation_sets_progress.append(
                    EvaluationSetProgress(
                        evaluation_set_id=row[0],
                        evaluation_set_name=row[1],
                        total_pkls=row[2],
                        annotated_pkls=row[3],
                        progress_percentage=float(row[4]),
                        last_updated=row[5],
                    )
                )

        # 2. 获取员工每日标注进度
        employee_daily_progress = []

        # 构建员工查询条件
        emp_conditions = ["pa.employee_id IS NOT NULL"]
        emp_params = []

        if employee_id:
            emp_conditions.append("pa.employee_id = %s")
            emp_params.append(employee_id)

        if start_date:
            emp_conditions.append("DATE(pa.created_at) >= %s")
            emp_params.append(start_date)

        if end_date:
            emp_conditions.append("DATE(pa.created_at) <= %s")
            emp_params.append(end_date)

        if evaluation_set_id is not None:
            emp_conditions.append("pa.evaluation_set_id = %s")
            emp_params.append(evaluation_set_id)

        emp_where = "WHERE " + " AND ".join(emp_conditions)

        # 查询员工每日标注数据（每个pkl_id算一个数据）
        emp_progress_query = f"""
        SELECT 
            pa.employee_id,
            DATE(pa.created_at) as annotation_date,
            COUNT(DISTINCT pa.pkl_id) as annotated_pkls_count
        FROM 
            pdp_path_annotation pa
        {emp_where}
        GROUP BY 
            pa.employee_id, DATE(pa.created_at)
        ORDER BY 
            pa.employee_id, annotation_date DESC
        """

        emp_result = execute_query(
            emp_progress_query, tuple(emp_params), fetch_all=True
        )

        if emp_result["success"] and emp_result["data"]:
            for row in emp_result["data"]:
                employee_daily_progress.append(
                    EmployeeProgress(
                        employee_id=row[0],
                        annotation_date=row[1],
                        annotated_pkls_count=row[2],
                    )
                )

        return AnnotationProgressResponse(
            success=True,
            evaluation_sets_progress=evaluation_sets_progress,
            employee_daily_progress=employee_daily_progress,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标注进展数据时出错: {str(e)}")


@router.get("/progress/summary")
async def get_annotation_summary(
    current_user: Optional[dict] = Depends(get_current_user),
):
    """
    获取标注进展概要统计
    """
    try:
        # 总体统计
        summary_query = """
        SELECT 
            COUNT(DISTINCT pa.pkl_id) as total_annotated_pkls,
            COUNT(DISTINCT pa.employee_id) as total_annotators,
            COUNT(DISTINCT pa.evaluation_set_id) as total_evaluation_sets,
            COUNT(*) as total_annotations
        FROM 
            pdp_path_annotation pa
        WHERE 
            pa.employee_id IS NOT NULL
        """

        summary_result = execute_query(summary_query, (), fetch_one=True)

        # 按标注类型统计
        annotation_type_query = """
        SELECT 
            pa.annotation,
            COUNT(DISTINCT pa.pkl_id) as pkl_count,
            COUNT(*) as annotation_count
        FROM 
            pdp_path_annotation pa
        WHERE 
            pa.employee_id IS NOT NULL
        GROUP BY 
            pa.annotation
        """

        type_result = execute_query(annotation_type_query, (), fetch_all=True)

        # 最近7天活跃度
        recent_activity_query = """
        SELECT 
            DATE(pa.created_at) as activity_date,
            COUNT(DISTINCT pa.employee_id) as active_annotators,
            COUNT(DISTINCT pa.pkl_id) as annotated_pkls
        FROM 
            pdp_path_annotation pa
        WHERE 
            pa.employee_id IS NOT NULL
            AND pa.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY 
            DATE(pa.created_at)
        ORDER BY 
            activity_date DESC
        """

        activity_result = execute_query(recent_activity_query, (), fetch_all=True)

        return {
            "success": True,
            "summary": {
                "total_annotated_pkls": summary_result["data"][0]
                if summary_result["success"] and summary_result["data"]
                else 0,
                "total_annotators": summary_result["data"][1]
                if summary_result["success"] and summary_result["data"]
                else 0,
                "total_evaluation_sets": summary_result["data"][2]
                if summary_result["success"] and summary_result["data"]
                else 0,
                "total_annotations": summary_result["data"][3]
                if summary_result["success"] and summary_result["data"]
                else 0,
            },
            "annotation_types": [
                {"annotation": row[0], "pkl_count": row[1], "annotation_count": row[2]}
                for row in (
                    type_result["data"]
                    if type_result["success"] and type_result["data"]
                    else []
                )
            ],
            "recent_activity": [
                {"date": row[0], "active_annotators": row[1], "annotated_pkls": row[2]}
                for row in (
                    activity_result["data"]
                    if activity_result["success"] and activity_result["data"]
                    else []
                )
            ],
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标注概要统计时出错: {str(e)}")


@router.get("/{evaluation_set_id}/cases")
async def get_evaluation_set_cases(
    evaluation_set_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    vin: Optional[str] = Query(None, description="VIN码精确匹配"),
    time_ns: Optional[str] = Query(None, description="时间戳(纳秒)"),
    time_range: Optional[str] = Query(None, description="时间范围(秒)"),
    fully_annotated_only: bool = Query(False),
    check_status: str = Query("all"),
):
    """
    获取评测集中的PKL文件列表，支持VIN码和时间范围搜索
    """
    try:
        # 构建基础查询
        base_query = """
        SELECT DISTINCT ecp.id, ecp.pkl_dir, ecp.pkl_name, ecp.vehicle_type, 
               ecp.vin, ecp.time_ns, ecp.key_obs_id, ecp.dirty_data, 
               ecp.created_at, esc.is_checked
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case esc ON ecp.id = esc.case_id
        WHERE esc.evaluation_set_id = %s
        """

        count_query = """
        SELECT COUNT(DISTINCT ecp.id)
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case esc ON ecp.id = esc.case_id
        WHERE esc.evaluation_set_id = %s
        """

        params = [evaluation_set_id]

        # 添加VIN码搜索条件
        if vin and vin.strip():
            base_query += " AND ecp.vin = %s"
            count_query += " AND ecp.vin = %s"
            params.append(vin.strip())

        # 添加时间范围搜索条件
        if time_ns and time_range:
            try:
                time_ns_int = int(time_ns)
                time_range_int = int(time_range)
                time_range_ns = time_range_int * 1000000000  # 转换为纳秒

                start_time = time_ns_int - time_range_ns
                end_time = time_ns_int + time_range_ns

                base_query += " AND ecp.time_ns >= %s AND ecp.time_ns <= %s"
                count_query += " AND ecp.time_ns >= %s AND ecp.time_ns <= %s"
                params.extend([start_time, end_time])
            except ValueError:
                raise HTTPException(
                    status_code=400, detail="时间戳和时间范围必须为有效数字"
                )

        # 添加其他筛选条件
        if fully_annotated_only:
            # 这里需要根据您的标注表结构来调整
            base_query += """
            AND ecp.id IN (
                SELECT DISTINCT pkl_id FROM pdp_path_annotation 
                WHERE evaluation_set_id = %s
                GROUP BY pkl_id
            )
            """
            count_query += """
            AND ecp.id IN (
                SELECT DISTINCT pkl_id FROM pdp_path_annotation 
                WHERE evaluation_set_id = %s
                GROUP BY pkl_id
            )
            """
            params.append(evaluation_set_id)

        if check_status == "checked":
            base_query += " AND esc.is_checked = 1"
            count_query += " AND esc.is_checked = 1"
        elif check_status == "unchecked":
            base_query += " AND esc.is_checked = 0"
            count_query += " AND esc.is_checked = 0"

        # 添加排序和分页
        base_query += " ORDER BY ecp.created_at DESC LIMIT %s OFFSET %s"
        offset = (page - 1) * page_size
        params.extend([page_size, offset])

        # 执行查询
        cases_result = execute_query(base_query, params)
        count_result = execute_query(
            count_query, params[:-2], fetch_one=True
        )  # 去掉LIMIT和OFFSET参数

        if not cases_result["success"] or not count_result["success"]:
            raise HTTPException(status_code=500, detail="查询失败")

        # 格式化返回数据
        formatted_cases = []
        for case in cases_result["data"]:
            formatted_cases.append(
                {
                    "id": case[0],
                    "pkl_dir": case[1],
                    "pkl_name": case[2],
                    "vehicle_type": case[3],
                    "vin": case[4],
                    "time_ns": case[5],
                    "key_obs_id": case[6],
                    "dirty_data": bool(case[7]),
                    "created_at": case[8],
                    "is_checked": bool(case[9]),
                }
            )

        return {
            "success": True,
            "data": formatted_cases,
            "total": count_result["data"][0] if count_result["data"] else 0,
            "page": page,
            "page_size": page_size,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取评测集案例失败: {str(e)}")
